"""
Unified Exception Hierarchy for Auto-Report Application

This module defines a layered exception architecture that enforces proper
error handling across the application layers:
- Presentation Layer: /app/main.py, /app/api, /app/cli
- Business Logic Layer: /app/agents, /app/workflows  
- Utility Layer: /app/utils

Key principles:
1. Boundary Exception Management: All exceptions crossing layer boundaries 
   must be caught and handled with comprehensive logging and context wrapping
2. Exception Flow Control Prohibition: Never use exceptions for normal program flow
3. Intra-Layer Exception Propagation: Exceptions within same layer propagate transparently
4. Exception Wrapping Requirements: Maintain full stack trace chain with meaningful context
5. Try-Catch Minimization: Only use try-catch when adding value (logging, context, recovery)
"""

import logging
import traceback
from typing import Optional, Dict, Any
from enum import Enum
from datetime import datetime


class ErrorSeverity(Enum):
    """Error severity levels for categorizing exceptions"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class LayerType(Enum):
    """Application layer types for exception context"""
    PRESENTATION = "presentation"
    API = "api"
    BUSINESS_LOGIC = "business_logic"
    UTILITY = "utility"


class FlexibleArgumentMixin:
    """
    Mixin class to provide flexible argument handling for exception constructors.

    This mixin standardizes how exceptions handle different calling patterns:
    1. Keyword arguments: ExceptionClass(message="...", suggested_action="...", context={...})
    2. Positional arguments: ExceptionClass("message", {"context": "dict"}, "suggested_action")
    3. Mixed patterns: Combination of both approaches
    """

    @staticmethod
    def _process_flexible_args(context_or_specific_param, suggested_action, specific_param_name, kwargs):
        """
        Process flexible argument patterns for exception constructors.

        Args:
            context_or_specific_param: Can be either:
                - str: specific parameter value (legacy usage)
                - dict: context information (common usage pattern)
                - None: no additional context
            suggested_action: Suggested action string (when context_or_specific_param is dict)
            specific_param_name: Name of the specific parameter (e.g., 'data_type', 'file_path')
            kwargs: Additional keyword arguments

        Returns:
            tuple: (specific_param_value, processed_kwargs)
        """
        # Handle legacy keyword argument pattern for specific parameter
        if specific_param_name in kwargs:
            specific_param_value = kwargs.pop(specific_param_name)
            if context_or_specific_param is None:
                context_or_specific_param = specific_param_value

        # Handle suggested_action passed as keyword argument
        if suggested_action is None and 'suggested_action' in kwargs:
            suggested_action = kwargs.get('suggested_action')

        # Handle different calling patterns
        if isinstance(context_or_specific_param, dict):
            # Pattern: ExceptionClass(message, context_dict, suggested_action)
            kwargs['context'] = context_or_specific_param
            if suggested_action:
                kwargs['suggested_action'] = suggested_action
            specific_param_value = context_or_specific_param.get(specific_param_name)
        elif isinstance(context_or_specific_param, str):
            # Pattern: ExceptionClass(message, specific_param)
            specific_param_value = context_or_specific_param
            if suggested_action:
                kwargs['suggested_action'] = suggested_action
        else:
            # Pattern: ExceptionClass(message, **kwargs)
            specific_param_value = context_or_specific_param  # Should be None
            if suggested_action:
                kwargs['suggested_action'] = suggested_action

        return specific_param_value, kwargs


class BaseAppException(Exception):
    """
    Base exception class for all application exceptions.
    
    Provides common functionality for error tracking, logging, and context management.
    All custom exceptions should inherit from this class.
    """
    
    def __init__(
        self,
        message: str,
        error_code: str,
        layer: LayerType,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
        suggested_action: Optional[str] = None
    ):
        """
        Initialize base application exception.
        
        Args:
            message: Human-readable error message
            error_code: Unique error code for tracking
            layer: Application layer where error occurred
            severity: Error severity level
            details: Additional technical details
            context: Contextual information (parameters, state, etc.)
            original_exception: Original exception that was wrapped
            suggested_action: Suggested action for user/developer
        """
        super().__init__(message)
        
        self.message = message
        self.error_code = error_code
        self.layer = layer
        self.severity = severity
        self.details = details
        self.context = context or {}
        self.original_exception = original_exception
        self.suggested_action = suggested_action
        self.timestamp = datetime.now()
        self.stack_trace = traceback.format_exc()
        self._logged = False  # Track whether this exception has been logged to prevent duplicates

        # Preserve original stack trace if wrapping another exception
        if original_exception:
            self.original_stack_trace = getattr(original_exception, 'stack_trace',
                                              traceback.format_exception(type(original_exception),
                                                                       original_exception,
                                                                       original_exception.__traceback__))
        else:
            self.original_stack_trace = None
    
    def get_full_context(self) -> Dict[str, Any]:
        """Get complete exception context for logging and debugging"""
        return {
            'error_code': self.error_code,
            'error_layer': self.layer.value,
            'error_severity': self.severity.value,
            'error_message': self.message,
            'error_details': self.details,
            'error_context': self.context,
            'suggested_action': self.suggested_action,
            'error_timestamp': self.timestamp.isoformat(),
            'original_exception': str(self.original_exception) if self.original_exception else None,
            'original_exception_type': type(self.original_exception).__name__ if self.original_exception else None
        }
    
    def log_error(self, logger: logging.Logger, force_log: bool = False) -> None:
        """Log the exception with appropriate level based on severity

        Args:
            logger: Logger instance to use
            force_log: Force logging even if already logged (for debugging)
        """
        # Prevent duplicate logging unless forced
        if self._logged and not force_log:
            logger.debug(f"[{self.error_code}] Exception already logged, skipping duplicate log")
            return

        # Create safe context that doesn't conflict with LogRecord attributes
        safe_context = {}
        full_context = self.get_full_context()

        # Only include non-conflicting keys in extra
        reserved_keys = {'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                        'filename', 'module', 'lineno', 'funcName', 'created',
                        'msecs', 'relativeCreated', 'thread', 'threadName',
                        'processName', 'process', 'message', 'exc_info', 'exc_text',
                        'stack_info', 'getMessage'}

        for key, value in full_context.items():
            if key not in reserved_keys:
                safe_context[key] = value

        log_message = f"[{self.error_code}] {self.layer.value.upper()} LAYER ERROR: {self.message}"
        if self.details:
            log_message += f" | Details: {self.details}"

        # Log with full stack trace only on first logging
        include_exc_info = not self._logged or force_log

        if self.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message, extra=safe_context, exc_info=include_exc_info)
        elif self.severity == ErrorSeverity.HIGH:
            logger.error(log_message, extra=safe_context, exc_info=include_exc_info)
        elif self.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message, extra=safe_context)
        else:
            logger.info(log_message, extra=safe_context)

        # Mark as logged to prevent duplicates
        self._logged = True
    
    def __str__(self) -> str:
        """String representation for user-facing error messages"""
        return f"[{self.error_code}] {self.message}"
    
    def __repr__(self) -> str:
        """Detailed representation for debugging"""
        return (f"{self.__class__.__name__}(message='{self.message}', "
                f"error_code='{self.error_code}', layer={self.layer}, "
                f"severity={self.severity})")


# ============================================================================
# PRESENTATION LAYER EXCEPTIONS
# ============================================================================

class PresentationLayerException(BaseAppException):
    """Base exception for presentation layer (main.py, API, CLI)"""
    
    def __init__(self, message: str, error_code: str, **kwargs):
        super().__init__(
            message=message,
            error_code=error_code,
            layer=LayerType.PRESENTATION,
            **kwargs
        )


class APIException(PresentationLayerException):
    """API-specific exceptions with HTTP status code support"""
    
    def __init__(self, message: str, error_code: str, http_status: int = 500, **kwargs):
        super().__init__(message=message, error_code=error_code, **kwargs)
        self.http_status = http_status


class AuthenticationException(APIException):
    """Authentication and authorization failures"""
    
    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(
            message=message,
            error_code="AUTH_001",
            http_status=401,
            severity=ErrorSeverity.HIGH,
            suggested_action="Please check credentials and try again",
            **kwargs
        )


class ValidationException(PresentationLayerException, FlexibleArgumentMixin):
    """Input validation failures"""

    def __init__(self, message: str, context_or_field_name=None, suggested_action=None, **kwargs):
        """
        Initialize ValidationException with flexible argument handling.

        Args:
            message: Error message
            context_or_field_name: Can be either:
                - str: field_name (legacy usage)
                - dict: context information (common usage pattern)
                - None: no additional context
            suggested_action: Suggested action string (when context_or_field_name is dict)
            **kwargs: Additional keyword arguments
        """
        # Handle legacy field_name parameter for backward compatibility
        if 'field_name' in kwargs:
            field_name = kwargs.pop('field_name')
            if context_or_field_name is None:
                context_or_field_name = field_name

        # Use the mixin to process flexible arguments
        field_name, processed_kwargs = self._process_flexible_args(
            context_or_field_name, suggested_action, 'field_name', kwargs
        )

        super().__init__(
            message=message,
            error_code="VAL_001",
            severity=ErrorSeverity.LOW,
            **processed_kwargs
        )
        if field_name:
            self.context = self.context or {}
            self.context['field_name'] = field_name


class UIException(PresentationLayerException):
    """User interface related exceptions"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code="UI_001",
            severity=ErrorSeverity.MEDIUM,
            suggested_action="Please refresh the page and try again",
            **kwargs
        )


class CLIException(PresentationLayerException):
    """Command line interface exceptions"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_code="CLI_001",
            severity=ErrorSeverity.MEDIUM,
            suggested_action="Please check command syntax and arguments",
            **kwargs
        )


# ============================================================================
# BUSINESS LOGIC LAYER EXCEPTIONS
# ============================================================================

class BusinessLogicException(BaseAppException):
    """Base exception for business logic layer (agents, workflows)"""

    def __init__(self, message: str, error_code: str, **kwargs):
        super().__init__(
            message=message,
            error_code=error_code,
            layer=LayerType.BUSINESS_LOGIC,
            **kwargs
        )


class WorkflowException(BusinessLogicException):
    """Workflow execution exceptions"""

    def __init__(self, message: str, workflow_name: str, step: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="WF_001",
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.context = self.context or {}
        self.context.update({
            'workflow_name': workflow_name,
            'step': step
        })


class AgentException(BusinessLogicException):
    """Agent processing exceptions"""

    def __init__(self, message: str, agent_type: str, **kwargs):
        super().__init__(
            message=message,
            error_code="AGENT_001",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        self.context = self.context or {}
        self.context['agent_type'] = agent_type


class RAGWorkflowException(WorkflowException):
    """RAG workflow specific exceptions"""

    def __init__(self, message: str, step: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            workflow_name="RAGWorkflow",
            step=step,
            error_code="RAG_001",
            **kwargs
        )


class ReportWorkflowException(WorkflowException):
    """Report workflow specific exceptions"""

    def __init__(self, message: str, step: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            workflow_name="ReportWorkflow",
            step=step,
            error_code="RPT_001",
            **kwargs
        )


class ModelException(BusinessLogicException):
    """LLM and model related exceptions"""

    def __init__(self, message: str, model_name: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_code="MODEL_001",
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        if model_name:
            self.context = self.context or {}
            self.context['model_name'] = model_name


# ============================================================================
# UTILITY LAYER EXCEPTIONS
# ============================================================================

class UtilityException(BaseAppException):
    """Base exception for utility layer (utils)"""

    def __init__(self, message: str, error_code: str, **kwargs):
        super().__init__(
            message=message,
            error_code=error_code,
            layer=LayerType.UTILITY,
            **kwargs
        )


class FileProcessingException(UtilityException, FlexibleArgumentMixin):
    """File processing and I/O exceptions"""

    def __init__(self, message: str, context_or_file_path=None, suggested_action=None, **kwargs):
        """
        Initialize FileProcessingException with flexible argument handling.

        Args:
            message: Error message
            context_or_file_path: Can be either:
                - str: file_path (legacy usage)
                - dict: context information (common usage pattern)
                - None: no additional context
            suggested_action: Suggested action string (when context_or_file_path is dict)
            **kwargs: Additional keyword arguments
        """
        # Use the mixin to process flexible arguments
        file_path, processed_kwargs = self._process_flexible_args(
            context_or_file_path, suggested_action, 'file_path', kwargs
        )

        super().__init__(
            message=message,
            error_code="FILE_001",
            severity=ErrorSeverity.MEDIUM,
            **processed_kwargs
        )
        if file_path:
            self.context = self.context or {}
            self.context['file_path'] = file_path


class DataProcessingException(UtilityException, FlexibleArgumentMixin):
    """Data processing and transformation exceptions"""

    def __init__(self, message: str, context_or_data_type=None, suggested_action=None, **kwargs):
        """
        Initialize DataProcessingException with flexible argument handling.

        Args:
            message: Error message
            context_or_data_type: Can be either:
                - str: data_type (legacy usage)
                - dict: context information (common usage pattern)
                - None: no additional context
            suggested_action: Suggested action string (when context_or_data_type is dict)
            **kwargs: Additional keyword arguments
        """
        # Use the mixin to process flexible arguments
        data_type, processed_kwargs = self._process_flexible_args(
            context_or_data_type, suggested_action, 'data_type', kwargs
        )

        super().__init__(
            message=message,
            error_code="DATA_001",
            severity=ErrorSeverity.MEDIUM,
            **processed_kwargs
        )
        if data_type:
            self.context = self.context or {}
            self.context['data_type'] = data_type


class ConfigurationException(UtilityException, FlexibleArgumentMixin):
    """Configuration and setup exceptions"""

    def __init__(self, message: str, context_or_config_key=None, suggested_action=None, **kwargs):
        """
        Initialize ConfigurationException with flexible argument handling.

        Args:
            message: Error message
            context_or_config_key: Can be either:
                - str: config_key (legacy usage)
                - dict: context information (common usage pattern)
                - None: no additional context
            suggested_action: Suggested action string (when context_or_config_key is dict)
            **kwargs: Additional keyword arguments
        """
        # Handle legacy config_key parameter for backward compatibility
        if 'config_key' in kwargs:
            config_key = kwargs.pop('config_key')
            if context_or_config_key is None:
                context_or_config_key = config_key

        # Use the mixin to process flexible arguments
        config_key, processed_kwargs = self._process_flexible_args(
            context_or_config_key, suggested_action, 'config_key', kwargs
        )

        super().__init__(
            message=message,
            error_code="CONFIG_001",
            severity=ErrorSeverity.HIGH,
            **processed_kwargs
        )
        if config_key:
            self.context = self.context or {}
            self.context['config_key'] = config_key


class ExternalServiceException(UtilityException):
    """External service integration exceptions"""

    def __init__(self, message: str, service_name: str, **kwargs):
        super().__init__(
            message=message,
            error_code="EXT_001",
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.context = self.context or {}
        self.context['service_name'] = service_name


# ============================================================================
# CROSS-LAYER EXCEPTION HANDLING FRAMEWORK
# ============================================================================

def wrap_layer_boundary_exception(
    original_exception: Exception,
    target_layer: LayerType,
    operation_context: str,
    logger: logging.Logger,
    additional_context: Optional[Dict[str, Any]] = None
) -> BaseAppException:
    """
    Wrap exceptions crossing layer boundaries with enhanced context.

    This function should be used at layer boundaries to wrap lower-layer
    exceptions with appropriate context and logging.

    Args:
        original_exception: The original exception to wrap
        target_layer: The layer that is handling/wrapping the exception
        operation_context: Description of the operation being performed
        logger: Logger instance for recording the exception
        additional_context: Additional context information

    Returns:
        Wrapped exception appropriate for the target layer
    """
    context = additional_context or {}
    context['operation_context'] = operation_context
    context['original_layer'] = getattr(original_exception, 'layer', 'unknown')

    # Determine appropriate exception type based on target layer
    if target_layer == LayerType.PRESENTATION:
        if isinstance(original_exception, (BusinessLogicException, UtilityException)):
            wrapped_exception = PresentationLayerException(
                message=f"Operation failed: {operation_context}",
                error_code="PRES_BOUNDARY_001",
                severity=ErrorSeverity.HIGH,
                details=str(original_exception),
                context=context,
                original_exception=original_exception,
                suggested_action="Please try again or contact support if the problem persists"
            )
        else:
            wrapped_exception = PresentationLayerException(
                message=f"Unexpected error during {operation_context}",
                error_code="PRES_BOUNDARY_002",
                severity=ErrorSeverity.CRITICAL,
                details=str(original_exception),
                context=context,
                original_exception=original_exception,
                suggested_action="Please contact support with error details"
            )

    elif target_layer == LayerType.BUSINESS_LOGIC:
        if isinstance(original_exception, UtilityException):
            wrapped_exception = BusinessLogicException(
                message=f"Business operation failed: {operation_context}",
                error_code="BIZ_BOUNDARY_001",
                severity=ErrorSeverity.HIGH,
                details=str(original_exception),
                context=context,
                original_exception=original_exception,
                suggested_action="Check input data and retry operation"
            )
        else:
            wrapped_exception = BusinessLogicException(
                message=f"Unexpected error in business logic: {operation_context}",
                error_code="BIZ_BOUNDARY_002",
                severity=ErrorSeverity.CRITICAL,
                details=str(original_exception),
                context=context,
                original_exception=original_exception,
                suggested_action="Contact development team for investigation"
            )

    elif target_layer == LayerType.UTILITY:
        wrapped_exception = UtilityException(
            message=f"Utility operation failed: {operation_context}",
            error_code="UTIL_BOUNDARY_001",
            severity=ErrorSeverity.MEDIUM,
            details=str(original_exception),
            context=context,
            original_exception=original_exception,
            suggested_action="Check system configuration and dependencies"
        )

    else:
        # Fallback for unknown layer
        wrapped_exception = BaseAppException(
            message=f"System error during {operation_context}",
            error_code="SYS_BOUNDARY_001",
            layer=target_layer,
            severity=ErrorSeverity.CRITICAL,
            details=str(original_exception),
            context=context,
            original_exception=original_exception,
            suggested_action="Contact system administrator"
        )

    # Log the wrapped exception
    wrapped_exception.log_error(logger)

    return wrapped_exception



